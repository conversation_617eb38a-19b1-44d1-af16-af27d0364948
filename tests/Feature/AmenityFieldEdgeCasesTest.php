<?php

namespace Tests\Feature;

use App\Models\Amenity;
use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AmenityFieldEdgeCasesTest extends TestCase
{
    use RefreshDatabase;

    protected User $client;
    protected User $admin;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = User::factory()->create(['role' => 'user']);
        $this->admin = User::factory()->create(['role' => 'admin']);
    }

    #[Test]
    public function field_with_large_number_of_amenities_renders_correctly()
    {
        $field = Field::factory()->create();
        
        // Create many amenities
        $amenities = [];
        for ($i = 1; $i <= 50; $i++) {
            $amenities[] = Amenity::factory()->create([
                'name' => "Amenity $i",
                'icon_class' => 'ri-star-line',
            ]);
        }
        
        $field->amenities()->attach($amenities);
        
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertSee('Amenity 1');
        $response->assertSee('Amenity 50');
        $response->assertDontSee('TypeError');
        $response->assertDontSee('Memory limit');
    }

    #[Test]
    public function amenity_with_special_characters_in_name_handled_correctly()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => 'Café & Restaurant (24/7)',
            'icon_class' => 'ri-restaurant-line',
        ]);
        
        $field->amenities()->attach($amenity->id);
        
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertSee('Café & Restaurant (24/7)');
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function amenity_with_very_long_name_handled_correctly()
    {
        $field = Field::factory()->create();
        $longName = str_repeat('Very Long Amenity Name ', 20); // ~400 characters
        $amenity = Amenity::factory()->create([
            'name' => $longName,
            'icon_class' => 'ri-text-line',
        ]);
        
        $field->amenities()->attach($amenity->id);
        
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertSee(substr($longName, 0, 50)); // Should see at least part of it
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function amenity_with_html_in_name_is_escaped()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => '<script>alert("xss")</script>Amenity',
            'icon_class' => 'ri-shield-line',
        ]);
        
        $field->amenities()->attach($amenity->id);
        
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        
        // Should see escaped HTML, not execute it
        $content = $response->getContent();
        $this->assertStringContainsString('&lt;script&gt;', $content);
        $this->assertStringNotContainsString('<script>alert', $content);
    }

    #[Test]
    public function amenity_with_null_icon_class_uses_fallback()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => 'No Icon Amenity',
            'icon_class' => null,
        ]);
        
        $field->amenities()->attach($amenity->id);
        
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertSee('No Icon Amenity');
        $response->assertSee('ri-check-line'); // fallback icon
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function amenity_with_empty_string_icon_class_uses_fallback()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => 'Empty Icon Amenity',
            'icon_class' => '',
        ]);
        
        $field->amenities()->attach($amenity->id);
        
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertSee('Empty Icon Amenity');
        $response->assertSee('ri-check-line'); // fallback icon
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function field_with_soft_deleted_amenities_handled_correctly()
    {
        $field = Field::factory()->create();
        $activeAmenity = Amenity::factory()->create(['name' => 'Active Amenity']);
        $deletedAmenity = Amenity::factory()->create(['name' => 'Deleted Amenity']);
        
        $field->amenities()->attach([$activeAmenity->id, $deletedAmenity->id]);
        
        // Soft delete one amenity
        $deletedAmenity->delete();
        
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertSee('Active Amenity');
        $response->assertDontSee('Deleted Amenity'); // Should not show soft deleted
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function concurrent_amenity_access_does_not_cause_issues()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create(['name' => 'Concurrent Test']);
        $field->amenities()->attach($amenity->id);
        
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        // Simulate multiple concurrent requests
        $responses = [];
        for ($i = 0; $i < 5; $i++) {
            $responses[] = $this->actingAs($this->client)
                ->get(route('bookings.show', $booking));
        }
        
        foreach ($responses as $response) {
            $response->assertStatus(200);
            $response->assertSee('Concurrent Test');
            $response->assertDontSee('TypeError');
        }
    }

    #[Test]
    public function amenity_relationship_survives_database_reconnection()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create(['name' => 'Reconnection Test']);
        $field->amenities()->attach($amenity->id);
        
        // Simulate database reconnection
        DB::disconnect();
        DB::reconnect();
        
        // Refresh models
        $field = Field::find($field->id);
        
        $this->assertEquals(1, $field->amenities->count());
        $this->assertEquals('Reconnection Test', $field->amenities->first()->name);
    }

    #[Test]
    public function amenity_with_unicode_characters_handled_correctly()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => '🏟️ Stadium Amenity 🎯',
            'icon_class' => 'ri-football-line',
        ]);
        
        $field->amenities()->attach($amenity->id);
        
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertSee('🏟️ Stadium Amenity 🎯');
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function get_available_amenities_handles_database_corruption_gracefully()
    {
        // Create some amenities first
        Amenity::factory()->create(['name' => 'Test Amenity', 'is_active' => true]);
        
        // Mock a database exception scenario
        // In a real scenario, this might be a connection issue or corrupted data
        $availableAmenities = Field::getAvailableAmenities();
        
        // Should always return an array, even if database fails
        $this->assertIsArray($availableAmenities);
        $this->assertNotEmpty($availableAmenities);
    }

    #[Test]
    public function amenity_collection_iteration_handles_modified_collection()
    {
        $field = Field::factory()->create();
        $amenities = Amenity::factory()->count(3)->create();
        $field->amenities()->attach($amenities);
        
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        // Modify amenities while potentially being accessed
        $amenities[0]->update(['name' => 'Modified During Access']);
        
        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function field_amenities_count_check_handles_null_relationship()
    {
        // Create a field but don't load the amenities relationship
        $field = Field::factory()->create();
        
        // This should not cause errors even if relationship is not loaded
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertDontSee('Amenities:'); // No amenities section
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function amenity_object_string_conversion_does_not_break_templates()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create(['name' => 'String Conversion Test']);
        $field->amenities()->attach($amenity->id);
        
        // Test that accidentally trying to use amenity as string doesn't break
        $amenityAsString = (string) $amenity; // This should work without errors
        $this->assertStringContainsString('String Conversion Test', $amenityAsString);
        
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertSee('String Conversion Test');
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function amenity_relationship_handles_orphaned_pivot_records()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create(['name' => 'Orphan Test']);
        $field->amenities()->attach($amenity->id);
        
        // Force delete the amenity but leave pivot record (simulating orphaned data)
        $amenityId = $amenity->id;
        $amenity->forceDelete();
        
        // Manually insert orphaned pivot record
        DB::table('field_amenity')->insert([
            'field_id' => $field->id,
            'amenity_id' => $amenityId,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        // Should handle orphaned records gracefully
        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertDontSee('TypeError');
        $response->assertDontSee('Orphan Test'); // Should not show deleted amenity
    }
}
