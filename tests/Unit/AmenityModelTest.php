<?php

namespace Tests\Unit;

use App\Models\Amenity;
use App\Models\Field;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(Amenity::class)]
class AmenityModelTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function amenity_has_correct_fillable_attributes()
    {
        $amenity = new Amenity;
        $fillable = $amenity->getFillable();

        $this->assertContains('name', $fillable);
        $this->assertContains('description', $fillable);
        $this->assertContains('icon_class', $fillable);
        $this->assertContains('is_active', $fillable);
    }

    #[Test]
    public function amenity_has_correct_casts()
    {
        $amenity = Amenity::factory()->create([
            'is_active' => 1,
            'created_at' => '2024-01-01 10:00:00',
            'updated_at' => '2024-01-01 11:00:00',
        ]);

        // Test boolean casting
        $this->assertIsBool($amenity->is_active);
        $this->assertTrue($amenity->is_active);

        // Test datetime casting
        $this->assertInstanceOf(\Carbon\Carbon::class, $amenity->created_at);
        $this->assertInstanceOf(\Carbon\Carbon::class, $amenity->updated_at);
    }

    #[Test]
    public function amenity_has_required_properties()
    {
        $amenity = Amenity::factory()->create([
            'name' => 'Test Amenity',
            'description' => 'Test Description',
            'icon_class' => 'ri-test-line',
            'is_active' => true,
        ]);

        // Test that all expected properties exist and are accessible
        $this->assertEquals('Test Amenity', $amenity->name);
        $this->assertEquals('Test Description', $amenity->description);
        $this->assertEquals('ri-test-line', $amenity->icon_class);
        $this->assertTrue($amenity->is_active);
        $this->assertIsInt($amenity->id);
    }

    #[Test]
    public function fields_relationship_returns_belongs_to_many()
    {
        $amenity = Amenity::factory()->create();
        
        $relationship = $amenity->fields();
        
        $this->assertInstanceOf(BelongsToMany::class, $relationship);
        $this->assertEquals('field_amenity', $relationship->getTable());
    }

    #[Test]
    public function fields_relationship_returns_collection_of_field_objects()
    {
        $amenity = Amenity::factory()->create();
        $field1 = Field::factory()->create();
        $field2 = Field::factory()->create();
        
        // Attach fields to amenity
        $amenity->fields()->attach([$field1->id, $field2->id]);
        
        $fields = $amenity->fields;
        
        $this->assertInstanceOf(Collection::class, $fields);
        $this->assertCount(2, $fields);
        $this->assertInstanceOf(Field::class, $fields->first());
        $this->assertTrue($fields->contains($field1));
        $this->assertTrue($fields->contains($field2));
    }

    #[Test]
    public function active_scope_returns_only_active_amenities()
    {
        Amenity::factory()->create(['is_active' => true, 'name' => 'Active Amenity']);
        Amenity::factory()->create(['is_active' => false, 'name' => 'Inactive Amenity']);

        $activeAmenities = Amenity::active()->get();

        $this->assertEquals(1, $activeAmenities->count());
        $this->assertTrue($activeAmenities->first()->is_active);
        $this->assertEquals('Active Amenity', $activeAmenities->first()->name);
    }

    #[Test]
    public function inactive_scope_returns_only_inactive_amenities()
    {
        Amenity::factory()->create(['is_active' => true, 'name' => 'Active Amenity']);
        Amenity::factory()->create(['is_active' => false, 'name' => 'Inactive Amenity']);

        $inactiveAmenities = Amenity::inactive()->get();

        $this->assertEquals(1, $inactiveAmenities->count());
        $this->assertFalse($inactiveAmenities->first()->is_active);
        $this->assertEquals('Inactive Amenity', $inactiveAmenities->first()->name);
    }

    #[Test]
    public function status_badge_class_attribute_returns_correct_classes()
    {
        $activeAmenity = Amenity::factory()->create(['is_active' => true]);
        $inactiveAmenity = Amenity::factory()->create(['is_active' => false]);

        $this->assertEquals('bg-success', $activeAmenity->status_badge_class);
        $this->assertEquals('bg-danger', $inactiveAmenity->status_badge_class);
    }

    #[Test]
    public function status_text_attribute_returns_correct_text()
    {
        $activeAmenity = Amenity::factory()->create(['is_active' => true]);
        $inactiveAmenity = Amenity::factory()->create(['is_active' => false]);

        $this->assertEquals('Active', $activeAmenity->status_text);
        $this->assertEquals('Inactive', $inactiveAmenity->status_text);
    }

    #[Test]
    public function icon_html_attribute_returns_correct_html()
    {
        $amenity = Amenity::factory()->create(['icon_class' => 'ri-test-line']);

        $expectedHtml = '<i class="ri-test-line"></i>';
        $this->assertEquals($expectedHtml, $amenity->icon_html);
    }

    #[Test]
    public function fields_count_attribute_returns_correct_count()
    {
        $amenity = Amenity::factory()->create();
        $field1 = Field::factory()->create();
        $field2 = Field::factory()->create();
        $field3 = Field::factory()->create();

        // Initially no fields
        $this->assertEquals(0, $amenity->fields_count);

        // Attach some fields
        $amenity->fields()->attach([$field1->id, $field2->id]);
        $amenity->refresh();

        $this->assertEquals(2, $amenity->fields_count);

        // Attach one more field
        $amenity->fields()->attach($field3->id);
        $amenity->refresh();

        $this->assertEquals(3, $amenity->fields_count);
    }

    #[Test]
    public function amenity_object_cannot_be_used_as_array_key()
    {
        $amenity = Amenity::factory()->create(['name' => 'Test Amenity']);
        $testArray = ['key1' => 'value1', 'key2' => 'value2'];

        // This should trigger a TypeError or return null when used as array key
        $this->expectException(\TypeError::class);
        $result = $testArray[$amenity];
    }

    #[Test]
    public function amenity_properties_are_accessible_as_object_properties()
    {
        $amenity = Amenity::factory()->create([
            'name' => 'Test Amenity',
            'description' => 'Test Description',
            'icon_class' => 'ri-test-line',
            'is_active' => true,
        ]);

        // These should all work without errors
        $this->assertIsString($amenity->name);
        $this->assertIsString($amenity->description);
        $this->assertIsString($amenity->icon_class);
        $this->assertIsBool($amenity->is_active);
        
        // Test that we can access these properties multiple times
        $name1 = $amenity->name;
        $name2 = $amenity->name;
        $this->assertEquals($name1, $name2);
    }

    #[Test]
    public function amenity_soft_deletes_work_correctly()
    {
        $amenity = Amenity::factory()->create(['name' => 'Test Amenity']);
        $amenityId = $amenity->id;

        // Soft delete the amenity
        $amenity->delete();

        // Should not be found in normal queries
        $this->assertNull(Amenity::find($amenityId));

        // Should be found with trashed
        $this->assertNotNull(Amenity::withTrashed()->find($amenityId));
        $this->assertNotNull($amenity->deleted_at);
    }

    #[Test]
    public function amenity_can_be_created_with_minimal_data()
    {
        $amenity = Amenity::create([
            'name' => 'Minimal Amenity',
            'icon_class' => 'ri-check-line',
            'is_active' => true, // Explicitly set since it's in fillable
        ]);

        $this->assertNotNull($amenity->id);
        $this->assertEquals('Minimal Amenity', $amenity->name);
        $this->assertEquals('ri-check-line', $amenity->icon_class);
        $this->assertNull($amenity->description);
        $this->assertTrue($amenity->is_active);
    }

    #[Test]
    public function amenity_name_must_be_unique()
    {
        Amenity::factory()->create(['name' => 'Unique Amenity']);

        $this->expectException(\Illuminate\Database\QueryException::class);
        Amenity::create([
            'name' => 'Unique Amenity', // Duplicate name
            'icon_class' => 'ri-test-line',
        ]);
    }
}
